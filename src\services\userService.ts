import type { DataItem, UserFormData, SearchParams, ApiResponse } from '../types/user';

/**
 * 模拟数据
 * 在实际项目中，这些数据应该从后端API获取
 */
export let mockData: DataItem[] = [
  {
    id: "1",
    name: "张三",
    age: 32,
    address: "北京市朝阳区",
    tags: ["开发", "前端"],
    createTime: "2023-07-20 12:00:00",
  },
  {
    id: "2",
    name: "李四",
    age: 42,
    address: "上海市浦东新区",
    tags: ["设计师"],
    createTime: "2023-07-19 14:30:00",
  },
  {
    id: "3",
    name: "王五",
    age: 32,
    address: "广州市天河区",
    tags: ["后端", "运维"],
    createTime: "2023-07-18 09:15:00",
  },
  {
    id: "31",
    name: "王五",
    age: 32,
    address: "广州市天河区",
    tags: ["后端", "运维"],
    createTime: "2023-07-18 09:15:00",
  },
  {
    id: "32",
    name: "王五",
    age: 32,
    address: "广州市天河区",
    tags: ["后端", "运维"],
    createTime: "2023-07-18 09:15:00",
  },
  // 更多模拟数据...
];

/**
 * 用户API服务类
 */
export class UserService {
  /**
   * 获取用户列表
   * @param params 搜索参数
   * @returns 用户列表和总数
   */
  static async getUsers(params: SearchParams): Promise<ApiResponse<DataItem>> {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 实际项目中的API调用示例：
    // const queryParams = {
    //   current: params.current?.toString() || '1',
    //   pageSize: params.pageSize?.toString() || '10',
    //   ...params
    // };
    // const response = await fetch(`/api/users?${new URLSearchParams(queryParams)}`);
    // return await response.json();

    for (let index = 0; index < 100; index++) {
      mockData.push({
        id: (index + 50).toString(),
        name: "王五",
        age: 32,
        address: "广州市天河区",
        tags: ["后端", "运维"],
        createTime: "2023-07-18 09:15:00",
      })
    }

    // 模拟分页和搜索逻辑
    let filteredData = [...mockData];
    
    // 根据搜索条件过滤数据
    if (params.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(params.name!)
      );
    }
    
    if (params.address) {
      filteredData = filteredData.filter(item => 
        item.address.includes(params.address!)
      );
    }
    
    if (params.age && params.age.length === 2) {
      filteredData = filteredData.filter(item => 
        item.age >= params.age![0] && item.age <= params.age![1]
      );
    }

    // 分页处理
    const { current = 1, pageSize = 10 } = params;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filteredData.length,
      success: true,
    };
  }

  /**
   * 删除单个用户
   * @param id 用户ID
   * @returns 是否删除成功
   */
  static async deleteUser(id: string): Promise<boolean> {
    console.log("删除用户:", id);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 50));
    
    // 实际项目中的API调用：
    // const response = await fetch(`/api/users/${id}`, { method: 'DELETE' });
    // return response.ok;
    
    return true; // 模拟成功
  }

  /**
   * 批量删除用户
   * @param ids 用户ID数组
   * @returns 是否删除成功
   */
  static async batchDeleteUsers(ids: string[]): Promise<boolean> {
    console.log("批量删除用户:", ids);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800));
    
    // 实际项目中的API调用：
    // const response = await fetch('/api/users/batch-delete', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({ ids })
    // });
    // return response.ok;
    
    return true; // 模拟成功
  }

  /**
   * 添加用户
   * @param userData 用户数据
   * @returns 新创建的用户信息
   */
  static async addUser(userData: UserFormData): Promise<DataItem> {
    console.log("添加用户:", userData);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 600));
    
    // 实际项目中的API调用：
    // const response = await fetch('/api/users', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(userData)
    // });
    // return await response.json();
    
    // 模拟返回新创建的用户
    const newUser: DataItem = {
      id: Date.now().toString(),
      ...userData,
      createTime: new Date().toLocaleString(),
    };
    
    return newUser;
  }

  /**
   * 更新用户
   * @param id 用户ID
   * @param userData 更新的用户数据
   * @returns 更新后的用户信息
   */
  static async updateUser(id: string, userData: Partial<UserFormData>): Promise<DataItem> {
    console.log("更新用户:", id, userData);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    // 实际项目中的API调用：
    // const response = await fetch(`/api/users/${id}`, {
    //   method: 'PUT',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(userData)
    // });
    // return await response.json();
    
    // 模拟返回更新后的用户
    const existingUser = mockData.find(user => user.id === id);
    if (!existingUser) {
      throw new Error('用户不存在');
    }
    
    return {
      ...existingUser,
      ...userData,
    };
  }
}
